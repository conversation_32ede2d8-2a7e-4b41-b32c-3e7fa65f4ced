import React from "react";
import Image from "next/image";
import { Body4, Body3, Body1Strong } from "../../../components/app-typography";

interface SubstackPostProps {
  title: string;
  description: string;
  img: string;
  link: string;
}

export default function SubstackPost({
  title,
  description,
  img,
  link,
}: SubstackPostProps) {
  return (
    <div className="mr-7 min-h-[272px] w-[300px] flex-none snap-center rounded-lg bg-back-accent-quaternary p-4 shadow-lg md:w-[370px] lg:w-[370px]">
      <div className="flex h-full flex-col justify-between">
        <Body4 color="primary" className="text-fore-on-accent-primary">
          {title}
        </Body4>
        <Body3 color="primary" className="text-fore-on-accent-primary">
          {description}
        </Body3>
        <Image
          src={img}
          alt={"Substack Image"}
          height={160}
          width={300}
          className="w-full object-cover"
        />
        <a
          href={link}
          target="_blank"
          rel="noreferrer"
          className="block underline"
        >
          <Body1Strong color="accent-alt" className="text-accent-alt-primary">
            Read &gt;
          </Body1Strong>
        </a>
      </div>
    </div>
  );
}
