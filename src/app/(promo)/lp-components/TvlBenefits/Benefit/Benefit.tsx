import React from "react";

interface BenefitProps {
  title: string;
  description: string;
  upcoming?: boolean;
  experimental?: boolean;
}

export default function Benefit({ title, description, upcoming, experimental }: BenefitProps) {
  return (
    <div
      className="relative mx-auto flex h-auto w-full max-w-[448px] flex-col items-center justify-start overflow-hidden rounded-lg bg-[rgba(103,80,164,0.12)] p-5 pt-10 text-center"
    >
      {upcoming && (
        <span className="absolute right-0 top-0 flex h-[36px] w-[176px] items-center justify-center rounded-bl-lg rounded-tr-lg bg-gradient-to-r from-[#EA5A4E] to-[#ED93D3] text-sm font-semibold text-white shadow-md">
          Upcoming Feature
        </span>
      )}
      <div className="flex size-full flex-col justify-between">
        <div className="flex w-full flex-col">
          <h2 className="w-full overflow-hidden break-words text-[24px] font-bold capitalize leading-none text-white md:text-[32px] lg:text-[52px]">
            {title}
          </h2>
        </div>
        <p className="mt-4 break-words text-[14px] text-white lg:text-[18px]">
          {description}
        </p>
        {experimental && (
          <p className="text-center text-[10px] font-light uppercase tracking-wider text-white lg:text-[14px]">
            Experimental
          </p>
        )}
      </div>
    </div>
  );
}
