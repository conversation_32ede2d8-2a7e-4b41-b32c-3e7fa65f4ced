"use client";

import { App<PERSON><PERSON>on } from "@/app/components/app-button";
import { AppInput } from "@/app/components/app-input";
import { AppPageTitle } from "@/app/components/app-page-title";
import { useGetMyOrg } from "@/app/services/organization.hooks";
import { useEffect, useState } from "react";
import axios from "axios";
import { Button } from "@/app/components/Button";
import { AppCode } from "@/app/components/app-code";
import { cn } from "@/lib/utils";
import { AppRadioGroup } from "@/app/components/app-radio-group";
import { AppCheckbox } from "@/app/components/app-checkbox";


// ADD check for for fork replay ? (vm warp and roll )

const workflowRadioOptions = [
  {
    label: "Scaffold Invariant Tests to coverage",
    value: "phase-2",
  },
  {
    label: "Identify Invariants and write tests(Coming VERY Soon)",
    value: null,
  },
  {
    label: "Find me some Bugs (Coming Soon)",
    value: null,
  },

];

export default function Magic() {

  const checkCanClone = async (repo: string, org: string) : Promise<boolean> => {
    try {
      await axios({
        method: "POST",
        url: `/api/jobs/canclone`,
        data: {
          orgName: org,
          repoName: repo,
        },
      });
      return true;
    } catch (e) {
    }

    return false;
  }
  
  

  const [orgName, setOrgName] = useState("");
  const [repoName, setRepoName] = useState("");
  const [branch, setBranch] = useState("");
  const { data: organization } = useGetMyOrg();

  const [workflowType, setWorkflowType] = useState("phase-2");

  const [isLoading, setIsLoading] = useState(false);
  const [isButtonLoading, setIsButtonLoading] = useState(false);

  const [isInviteOrg, setIsInviteOrg] = useState(true); // TODO: Add this option
  // TODO: Add ability to specify custom recipients as well

  const [allMagicJobs, setAllMagicJobs] = useState([]);

  const handleWorkflowType = (e) => {
    if(e === null) return;
    setWorkflowType(e);
  };

  const parseURI = (inputValue) => {
    console.log("input value", inputValue);
    const success = /^(https?:\/\/)?(www\.)?github\.com\/.+\/.+(\.git)?$/.test(
      inputValue
    );

    if (!success) {
      alert( "Invalid GitHub URL");
      return;
    }

    const ghLink = inputValue.endsWith("/")
      ? inputValue.slice(0, -1)
      : inputValue;
    const uriParts = ghLink
      .replace("https://", "")
      .replace("http://", "")
      .split("/");

    if (uriParts.length >= 3) {
      const orgName = uriParts[1];
      const repoName = uriParts[2];
      let ref = "main"; // Default branch

      // Check if the URL specifies a branch
      if (uriParts.length > 5 && uriParts[3] === "tree") {
        // The branch name can include slashes, so we join the remaining parts
        ref = uriParts.slice(4).join("/");
      } else if (uriParts.length === 5 && uriParts[3] === "tree") {
        // Handle the case where there's no slash in the branch name
        ref = uriParts[4];
      }

      // Set the values to the form
      setOrgName(orgName);
      setRepoName(repoName);
      setBranch(ref);
    }
  };

  const fetchAllMagicJobs = async () => {
    const response = await axios.get("/api/claude/jobs");
    setAllMagicJobs(response.data.data);
  };

  useEffect(() => {
    fetchAllMagicJobs();
  }, []);

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if(isLoading) return;

    setIsLoading(true);

    if(!organization) {
      alert("You must be a member of an organization to use this feature");
      return;
    }

    if(!orgName) {
      alert("You must enter a repo name to use this feature");
      return;
    }

    if(!repoName) {
      alert("You must enter a repo name to use this feature");
      return;
    }
    
    if(!branch) {
      alert("You must enter a branch name to use this feature");
      return;
    }

    // Already caught in the checkCanClone function
    const canClone = await checkCanClone(repoName, orgName);
    if(!canClone) {
      alert("Recon doesn't have access to this repository, please authorize the Recon app on the repo");
      setIsLoading(false);
      return;
    }
    

    try {
      const response = await axios.post("/api/claude/jobs", {
        organizationId: organization?.id,
        orgName,
        repoName,
        branch,
      });

      console.log(response);
    } catch (error) {
      console.error(error);
    }

    setIsLoading(false);
    setOrgName("");
    setRepoName("");
    setBranch("");
    fetchAllMagicJobs();
  };

  const deleteMagicJobHandler = async (jobId: string) => {
    try {
      setIsButtonLoading(true);
      const response = await axios.delete("/api/claude/jobs", {
        data: {
          jobId,
          },
      });
      console.log(response);
    } catch (error) {
      console.error(error);
      alert(`Failed to delete job:\n ${error.response.data.message}`);
    }
    fetchAllMagicJobs();
    setIsButtonLoading(false);
  }

  return (
    <div className="min-h-screen bg-dashboardBG">

      <div className="min-h-[65vh] overflow-auto p-[45px]">
        <div className="mb-[20px] mt-[40px] text-[15px] leading-[18px] text-textSecondary">
          <AppPageTitle>Recon Magic</AppPageTitle>
          <p>Give us a Repo with scaffolded Chimera Invariant Tests, we'll get them to coverage</p>
          <p>NOTE: Only super admin can use this feature! DM us for an early preview!</p>
        </div>

        <div className="flex flex-col gap-4">
          <form onSubmit={handleSubmit}>
            <AppInput
                label="Paste Github URL for your convenience"
                onChange={(e) => {
                  parseURI(e.target.value); // additional logic for parsing URL
                }}
                type="text"
                placeholder="Enter GitHub Repo URL"
                error={""}

            />
            <AppInput
              label="OrgName"
              value={orgName}
              onChange={(e) => setOrgName(e.target.value)}
            />
            <AppInput
              label="RepoName"
              value={repoName}
              onChange={(e) => setRepoName(e.target.value)}
            />
            <AppInput
              label="Branch"
              value={branch}
              onChange={(e) => setBranch(e.target.value)}
            />
            <p className="mb-[9px] text-[15px] leading-[18px] text-textSecondary">
              Select Job Type
            </p>
            <AppRadioGroup
              name="WorkflowType"
              options={workflowRadioOptions}
              onChange={(e) => handleWorkflowType(e)}
              value={workflowType}
            />

            <div className="flex mt-[20px]">
              <AppButton type="submit" disabled={isLoading}>{isLoading ? "Loading..." : "Start Job"}</AppButton>
            </div>
          </form>
        </div>

        <div className="mb-[20px] mt-[40px] text-[15px] leading-[18px] text-textSecondary">
          <h3 className="text-[21px] leading-[28px]">All Magic Jobs</h3>
          <div className="flex flex-col gap-[20px]">
          {allMagicJobs.map((job) => (
            <div
              key={job.id}
              className={cn(
                "py-[20px] px-[22px] justify-between bg-blockBg gradient-dark-bg rounded-t-[8px] border-divider border border-b-0 rounded-b-[8px] border-b-1"
              )}
            >
              <div className="text-[18px] leading-[21px] text-textPrimary">
                <div className="flex items-right mb-3 mt-3">
                </div>
                <AppCode
                  code={JSON.stringify(job, null, 2)}
                  language="json"
                />
                <div className="flex justify-end mt-[20px]">
                <Button
                    disabled={isButtonLoading}
                    onClick={() => deleteMagicJobHandler(job.id)}
                  >
                    {isButtonLoading
                      ? "Loading"
                      : "Delete this Job (only b4 it starts)"}
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
        </div>
      </div>
    </div>
  );
}
