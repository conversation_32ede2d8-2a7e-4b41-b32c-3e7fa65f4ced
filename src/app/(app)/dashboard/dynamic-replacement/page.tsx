"use client";

import { useState } from "react";

import { ENV_TYPE } from "@/app/app.constants";
import { CreateJobForm } from "@/app/components/create-job-form/create-job-form";
import { PageHeader } from "@/app/components/page-header";
import { Webhooks } from "@/app/components/Webhooks";
import { useJobSubmission } from "@/app/hooks/useJobSubmission";
import { useGetJobs } from "@/app/services/jobs.hooks";
import { useGetMyOrg } from "@/app/services/organization.hooks";
import { AppPageHeader } from "../../components/app-page-header";

export interface PreparedDynamicReplacementContract {
  target: string;
  replacement: string;
  endOfTargetMarker: string;
  targetContract: string;
}
export default function JobsPage() {
  const [env, setEnv] = useState(ENV_TYPE.MEDUSA);
  const [jobId, setJobId] = useState<null | number>(null);

  const { data: allJobs, refetch: refetchJobs } = useGetJobs();
  const { data: organization } = useGetMyOrg();

  const { getSubmissionHandler } = useJobSubmission({
    organization,
    allJobs,
    setJobId,
    refetchJobs,
  });

  const onSubmit = getSubmissionHandler(env);

  return (
    <div className="flex flex-col gap-6 px-[200px] py-6">
      <Webhooks />
      <div className="px-10">
        <AppPageHeader
          title="Dynamic Replacement"
          descriptions={[
            "Dynamic Replacement is in EXPERIMENTAL mode",
            "All variables Dynamically Replaced MUST be in the `Setup.sol` file",
            "Make sure you have no clashing file!",
          ]}
        />

        <CreateJobForm
          title=""
          submitLabel="Run Job"
          {...{
            env,
            jobId,
            setEnv,
          }}
          onSubmit={onSubmit}
          dynamicReplacement={true}
        />
      </div>
    </div>
  );
}
