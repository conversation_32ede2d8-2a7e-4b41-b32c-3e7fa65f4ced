"use client";

import React from "react";
import Image from "next/image";
import { H6 } from "../../../components/app-typography";

const SAVED_PROJECTS = [
  {
    name: "Centrifuge",
    logo: "/centrifuge-logo.svg",
    alt: "Centrifuge Logo",
  },
  {
    name: "Badger",
    logo: "/badger-logo.svg",
    alt: "Badger Logo",
  },
  {
    name: "Liqui<PERSON>",
    logo: "/Liquity.svg",
    alt: "Liquity Logo",
  },
];

interface ProjectLogoProps {
  project: (typeof SAVED_PROJECTS)[0];
}

const ProjectLogo = ({ project }: ProjectLogoProps) => {
  return (
    <div className="flex h-12 items-center justify-center rounded-lg  px-6">
      <Image
        src={project.logo}
        alt={project.alt}
        width={120}
        height={32}
        className="h-8 w-auto object-contain"
      />
    </div>
  );
};

const DashboardSavedProjects = () => {
  return (
    <div className="rounded-xl bg-back-neutral-secondary p-6 backdrop-blur-sm">
      <div className="flex items-center justify-between">
        <H6>Helping these projects deploy safely</H6>

        <div className="flex items-center gap-6">
          {SAVED_PROJECTS.map((project) => (
            <ProjectLogo key={project.name} project={project} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default DashboardSavedProjects;
