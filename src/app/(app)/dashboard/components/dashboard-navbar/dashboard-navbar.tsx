"use client";
import { usePathname } from "next/navigation";
import { <PERSON>aB<PERSON>, FaD<PERSON>rd } from "react-icons/fa";

import { useGetDiscord } from "@/app/services/discord.hook.ts";
import { OrgStatus, useGetMyOrg } from "@/app/services/organization.hooks";

import { SidebarItem } from "./sidebar-item";
import { UpgradeToPro } from "./upgrade-to-pro";
import { useGetSidebarLinks } from "./use-get-sidebar-links";

export const DashboardNavbar = () => {
  const { items, isChildItemsLoading } = useGetSidebarLinks();
  const { orgStatus } = useGetMyOrg();

  const { data: discordUrl } = useGetDiscord();
  const pathname = usePathname();

  const hideSidebar =
    pathname.includes("/shares/") || pathname.includes("/tools/medusa");

  if (hideSidebar) return null;

  return (
    // eslint-disable-next-line tailwindcss/no-custom-classname
    <aside className="aside-menu min-w-[300px] max-w-[300px] overflow-y-auto bg-back-neutral-secondary pb-[27px]">
      <ul className="mt-[24px]">
        {items.map((item) => (
          <SidebarItem
            key={item.label}
            {...item}
            isChildItemsLoading={isChildItemsLoading}
          />
        ))}
      </ul>
      <a
        href={discordUrl}
        target="_blank"
        rel="nofollow noreferrer"
        className="cursor-pointer"
      >
        <div className="mb-[55px] ml-[28px] mt-[24px] flex gap-[18px] text-[16px] leading-[19px] text-fore-neutral-primary">
          <FaDiscord className="h-[16px] w-[20px]" />
          Discord
        </div>
      </a>
      <a
        href={"https://book.getrecon.xyz/"}
        target="_blank"
        rel="nofollow noreferrer"
        className="cursor-pointer"
      >
        <div className="mb-[55px] ml-[28px] mt-[24px] flex gap-[18px] text-[16px] leading-[19px] text-fore-neutral-primary">
          <FaBook className="h-[16px] w-[20px]" />
          Tutorials
        </div>
      </a>

      {orgStatus === OrgStatus.FREE && <UpgradeToPro />}
    </aside>
  );
};
