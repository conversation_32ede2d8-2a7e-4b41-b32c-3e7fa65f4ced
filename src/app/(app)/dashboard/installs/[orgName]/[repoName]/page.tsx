"use client";

import axios from "axios";
import { useState } from "react";

import { AppInput } from "@/app/components/app-input";
import { AppPageTitle } from "@/app/components/app-page-title";
import { AppButton } from "@/app/components/app-button";
import { Loading } from "@/app/components/Loading";
import { useGetInstalls } from "@/app/services/installs.hook";

export default function SingleRepoPage({
  params,
}: {
  params: { orgName: string; repoName: string };
}) {
  const { data: installs, isLoading } = useGetInstalls();

  // Given OrgName
  // Given Repo Name
  // Fetch all branches

  const [useDefaultBranch, setUserDefaultBranch] = useState(true);
  const [customBranch, setCustomBranch] = useState("");

  // TODO: Fetch All Branches
  // Ideally sorted by last edited
  // One Off get Branches

  const foundInstall = installs
    ? installs.find(function (install) {
        return install.full_name == `${params.orgName}/${params.repoName}`;
      })
    : null;

  // TODO: Trigger build and get JOB ID

  const startBuild = async () => {
    if (!foundInstall) {
      alert("Not found install!");
      return;
    }

    try {
      const jobStartRes = await axios({
        method: "POST",
        url: `/api/build/`,
        data: {
          orgName: params.orgName,
          repoName: params.repoName,
          branch: useDefaultBranch ? foundInstall.default_branch : customBranch,
        },
      });
      console.log("jobStartRes", jobStartRes);
      alert(`JobStartRes ${jobStartRes.data}`);
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }
  };

  // cpmst

  return (
    <div className="pl-[45px] pt-[45px]">
      <AppPageTitle>Specific private repo install page</AppPageTitle>
      {isLoading && <Loading />}
      {!isLoading && (
        <>
          {foundInstall && (
            <ul className="py-[20px]">
              {[
                `Org: ${params.orgName ?? ""}`,
                `Repo: ${params.repoName ?? ""}`,
                `Default: ${foundInstall.default_branch ?? ""}`,
              ]
                .filter(Boolean)
                .map((line) => (
                  <p
                    key={line}
                    className="mb-[15px] w-[100%] border-x-0 border-b-stroke-neutral-decorative pb-[14px] text-[15px] leading-[18px] text-fore-neutral-secondary"
                  >
                    {line}
                  </p>
                ))}
            </ul>
          )}

          <div className="flex items-center gap-[20px]">
            <AppButton onClick={() => setUserDefaultBranch(!useDefaultBranch)}>
              Select a custom branch | TODO
            </AppButton>
            {!useDefaultBranch && (
              <div className="flex items-center gap-[20px]">
                <p className=" text-[20px] leading-[25px] text-fore-neutral-secondary">
                  Branch Name
                </p>
                <AppInput
                  type="text"
                  onChange={(e) => setCustomBranch(e.target.value)}
                  value={customBranch}
                />
              </div>
            )}
            <AppButton onClick={startBuild}>Add Branch to Recon!</AppButton>
          </div>
        </>
      )}
    </div>
  );
}
