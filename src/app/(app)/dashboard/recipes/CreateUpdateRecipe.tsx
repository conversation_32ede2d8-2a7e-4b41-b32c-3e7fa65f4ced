"use client";

import axios from "axios";
import { useEffect, useState } from "react";
import { FormProvider } from "react-hook-form";

import { ENV_TYPE } from "@/app/app.constants";
import { SubFormFoundry } from "@/app/components/create-job-form/subform-foundry";
import {
  useGetRecipeByIdentifier,
  useGetRecipes,
} from "@/app/services/recipes.hook";

import { SubFormHalmos } from "@/app/components/create-job-form/subform-halmos";
import { AppButton } from "../../../components/app-button";
import { AppInput } from "../../../components/app-input";
import { AppSpinner } from "../../../components/app-spinner";
import { SubFormEchidna } from "../../../components/create-job-form/subform-echidna";
import { SubFormMedusa } from "../../../components/create-job-form/subform-medusa";
import { JobTypeFuzzer } from "../../../components/job-type-fuzzer";

import { useFormLogic } from "@/app/components/create-job-form/hooks/useFormLogic";
import type {
  GitHubLinkFormValues,
  RecipeFormProps,
} from "@/app/components/create-job-form/types";
import { parseGitHubURL } from "@/app/components/create-job-form/utils";

export function CreateUpdateRecipeForm({
  editId,
  title = "Create Recipe",
  submitLabel = "Create Recipe",
}: RecipeFormProps) {
  const {
    form,
    register,
    handleSubmit,
    setValue,
    setError,
    watch,
    errors,
    isSubmitting,
  } = useFormLogic("recipe");

  const { refetch: refetchRecipes } = useGetRecipes();

  // TODO Githublink form values
  const onSubmit = async ({
    orgName,
    repoName,
    ref,
    directory,
    medusaConfig,
    timeout,
    preprocess,
    displayName,

    pathToTester,
    contract,
    corpusDir,
    echidnaConfig,
    forkBlock,
    forkMode,
    forkReplacement,
    rpcUrl,
    testLimit,
    testMode,
    targetCorpus,

    runs,
    seed,
    verbosity,
    testCommand,
    testTarget,

    halmosArray,
    halmosLoops,
    halmosPrefix,
  }: GitHubLinkFormValues) => {
    const fuzzerArgs =
      env == ENV_TYPE.MEDUSA
        ? {
            timeout,
            config: medusaConfig,
            targetCorpus,
          }
        : env == ENV_TYPE.ECHIDNA
          ? {
              pathToTester,
              config: echidnaConfig,
              contract,
              corpusDir,
              forkBlock,
              forkMode,
              forkReplacement,
              rpcUrl,
              testLimit,
              testMode,
              targetCorpus,
            }
          : env == ENV_TYPE.FOUNDRY
            ? {
                contract,
                forkBlock,
                forkMode,
                rpcUrl,
                runs,
                seed,
                testCommand,
                testTarget,
                verbosity,
              }
            : {
                contract,
                testCommand,
                verbosity,
                halmosArray,
                halmosLoops,
                halmosPrefix,
              };

    // TODO: CONDITION: If we have `editId` then this is an update
    // Else it's a create

    if (editId) {
      const recipeData = {
        fuzzer: env,
        displayName,
        fuzzerArgs,
        preprocess,
        orgName,
        repoName,
        ref,
        directory,
      };

      try {
        // This is a UPDATE
        const foundData = await axios({
          method: "POST",
          url: `/api/recipes/${editId}`,
          data: {
            recipeId: editId,
            recipeData,
          },
        });
        alert("Updated recipe!");
      } catch (e) {
        console.log("e", e);
        alert(`Something went wrong: ${e.response.data.message}`);
      }
    }

    // It's a create
    if (!editId) {
      try {
        const foundData = await axios({
          method: "POST",
          url: `/api/recipes`,
          data: {
            fuzzer: env, /// @audit Necessary
            displayName,
            fuzzerArgs,
            preprocess,
            orgName,
            repoName,
            ref,
            directory,
          },
        });

        refetchRecipes();
      } catch (e) {
        console.log("e", e);
        alert(`Something went wrong: ${e.response.data.message}`);
      }
    }
  };

  const recipe = useGetRecipeByIdentifier(editId);
  const [doneLoading, setDoneLoading] = useState(false);

  // UPDATE RECIPE
  // We can pass an ID, when the ID is passed that means we're updating tge
  useEffect(() => {
    async function setPresets() {
      console.log("recipe", recipe);

      // @ts-expect-error we'd have to cast and verify but this is always valid
      setEnv(recipe.data.fuzzer);

      Object.keys(recipe.data).forEach((key) => {
        console.log("key", key);
        // @ts-expect-error we purposefully don't care, since the form will ignore extra fields anyway
        setValue(key.toString(), recipe.data[key]);
      });

      Object.keys(recipe.data?.fuzzerArgs).forEach((key) => {
        console.log("key", key);
        // @ts-expect-error See above
        setValue(key.toString(), recipe.data?.fuzzerArgs[key]);
      });
    }

    if (recipe?.data?.fuzzer && !doneLoading) {
      setPresets();
      setDoneLoading(true);
    }
  }, [recipe, doneLoading, setValue]);

  const [env, setEnv] = useState(ENV_TYPE.MEDUSA);

  const githubUrlRegister = register("githubURL");

  return (
    <FormProvider
      {...({
        register,
        handleSubmit,
        setValue,
        setError,
        watch,
        errors,
        isSubmitting,
      } as any)}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <h3 className="mb-[22px] text-[28px] leading-[33px] text-fore-neutral-primary">
          {title}
        </h3>

        <div className="mb-6 w-full">
          {env && (
            <JobTypeFuzzer value={env} onChange={(value) => setEnv(value)} />
          )}
        </div>
        <div className="flex flex-col gap-6 self-stretch">
          <div className="flex flex-col gap-6 self-stretch">
            <AppInput
              label="Recipe Display Name"
              {...register("displayName")}
              type="text"
            />

            <AppInput
              {...githubUrlRegister}
              onChange={(e) => {
                githubUrlRegister.onChange(e); // default react-hook-form onChange
                parseGitHubURL(e.target.value, setValue, setError); // additional logic for parsing URL
              }}
              type="text"
              label="GitHub Repo URL"
              placeholder="Enter GitHub Repo URL"
              error={errors.githubURL?.message}
            />
          </div>

          <div className="h-px w-full bg-white/10" />

          <div className="flex flex-col gap-4 self-stretch">
            <h3 className="text-xl font-bold leading-[1.3] text-[#F5F5F5]">
              Or specify the organization, repository and branch directly
            </h3>

            <div className="flex w-full flex-col gap-6 self-stretch">
              <div className="flex gap-6">
                <AppInput
                  label="Organization"
                  {...register("orgName")}
                  type="text"
                />
                <AppInput {...register("repoName")} type="text" label="Repo" />
              </div>

              <div className="flex flex-row gap-6 self-stretch">
                <AppInput {...register("ref")} type="text" label="Branch" />
                <AppInput
                  {...register("directory")}
                  type="text"
                  label="Directory"
                />
              </div>
            </div>

            {!env && (
              <AppInput
                {...register("customOut")}
                type="text"
                label="Custom output folder"
              />
            )}
          </div>

          <div className="h-px w-full bg-white/10" />

          {env && (
            <div className="flex flex-col gap-4 self-stretch">
              <h3 className="text-xl font-bold leading-[1.3] text-[#F5F5F5]">
                Configure custom parameters for {env.toLowerCase()}:
              </h3>

              <div className="flex flex-col gap-8 self-stretch">
                {env === ENV_TYPE.MEDUSA ? (
                  <SubFormMedusa />
                ) : env === ENV_TYPE.ECHIDNA ? (
                  <SubFormEchidna />
                ) : env === ENV_TYPE.FOUNDRY ? (
                  <SubFormFoundry />
                ) : (
                  <SubFormHalmos />
                )}
              </div>
            </div>
          )}

          <div className="h-px w-full bg-white/10" />

          <AppButton
            type="submit"
            disabled={isSubmitting}
            size="lg"
            fullWidth
            className="flex flex-row items-center justify-center gap-1 self-stretch rounded-lg px-3 py-2"
          >
            {isSubmitting ? <AppSpinner /> : submitLabel}
          </AppButton>
        </div>
      </form>
    </FormProvider>
  );
}
