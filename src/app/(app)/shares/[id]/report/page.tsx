"use client";
import { useEffect, useState } from "react";
import axios from "axios";

import { AppButton } from "@/app/components/app-button";
import { downloadFile } from "@/app/(app)/tools/mdReportHelper";
import { generateJobMD, processLogs } from "@recon-fuzz/log-parser";
import type { Job } from "@/app/services/jobs.hooks";
import type { Fuzzer, FuzzingResults } from "@recon-fuzz/log-parser";
import Link from "next/link";
import { AppLogo } from "@/app/components/app-logo";
import JobReport from "@/app/components/JobReport/JobReport";
import LogComponent from "@/app/(app)/tools/logs-parser/logs-parser";
import type { ENV_TYPE } from "@/app/app.constants";

export default function ShareReport({
  params: { id },
}: {
  params: { id: string };
}) {
  const [jobData, setJobData] = useState<Job | null>(null);
  const [md, setMd] = useState("");
  const [jobStats, setJobStats] = useState<FuzzingResults | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function getJobInfo() {
      if (!id) {
        return;
      }
      try {
        const res = await axios.get(`/api/shares/${id}`);
        setJobData(res?.data?.data?.job);
        setIsLoading(false);
      } catch (e) {
        console.log(e);
        setIsLoading(false);
      }
    }
    getJobInfo();
  }, [id]);

  useEffect(() => {
    async function getLogs() {
      const jobLogsRaw = await axios({
        method: "POST",
        url: `/api/fetchLogs`,
        data: {
          logsUrl: jobData?.logsUrl,
        },
      });
      const data = processLogs(jobLogsRaw.data, jobData?.fuzzer as Fuzzer);
      const jobStats = data;
      setJobStats(jobStats);
      const md = generateJobMD(
        jobData?.fuzzer as Fuzzer,
        jobLogsRaw.data,
        jobData?.label || `${jobData?.orgName}/${jobData?.repoName}`
      );
      setMd(md);
      setIsLoading(false);
    }
    getLogs();
  }, [jobData]);

  return (
    <div className="min-h-screen grow overflow-y-auto bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <div className="gradient-dark-bg flex items-center justify-between bg-back-neutral-tertiary px-[40px] py-[20px]">
        <Link href="/dashboard" className="cursor-pointer">
          <AppLogo />
        </Link>
      </div>
      {isLoading === false ? (
        <div className="p-10">
          <h2 className="mb-2 text-4xl text-white">
            Share report for {jobData?.orgName}/{jobData?.repoName}/
            {jobData?.ref}
          </h2>
          <div className="flex flex-row justify-between">
            {md ? (
              <AppButton
                className="bg-accent-primary px-[14px] py-[9px] leading-[21px]"
                onClick={() => downloadFile(md, jobData.repoName)}
              >
                <span>Download report</span>
              </AppButton>
            ) : (
              ""
            )}
            <Link
              href={`/shares/${id}`}
              className="w-[170px] rounded-md bg-accent-primary px-[14px] py-[9px] leading-[21px] text-white"
            >
              Back to Run Results
            </Link>
          </div>
          {md && jobData && jobStats ? (
            <>
              <JobReport
                fuzzer={jobData.fuzzer}
                jobStats={jobStats}
                showBrokenProp={true}
              />
              <LogComponent
                fuzzer={jobData?.fuzzer as ENV_TYPE}
                logs={jobData.brokenProperties.join("\n\n")}
                jobStatsForced={jobStats}
              />
            </>
          ) : (
            ""
          )}
        </div>
      ) : (
        <h2 className="text-center text-4xl text-white">Loading...</h2>
      )}
    </div>
  );
}
