import { ENV_TYPE } from "@/app/app.constants";
import type { Metadata } from "next";
import ToolPageLayout from "../../components/tool-layout";

export const metadata: Metadata = {
  title: "Medusa Logs Parser",
  description:
    "Paste Medusa Logs and automatically convert broken properties into Foundry Repros",
};

export default function MedusaParserPage() {
  return (
    <ToolPageLayout
      toolType={ENV_TYPE.MEDUSA}
      toolName="Medusa Logs Scraper"
      toolDescription={[
        "This tool allows to scrape medusa logs for broken properties repros",
        "Paste your raw medusa logs, and the tool will generate foundry repros for you",
      ]}
      youtubeUrl="https://www.youtube.com/embed/8-qWL2Dcgpc"
      youtubeOverlayText="Learn how to use Medusa"
    />
  );
}
