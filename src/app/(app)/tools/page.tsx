import { AppButton } from "@/app/components/app-button";
import type { Metada<PERSON> } from "next";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Recon Free Tools",
  description: "A collection of tools for you to secure your smart contracts",
};

function ToolsList() {
  return (
    <>
      <h2 className="mb-8 pl-4 text-3xl font-bold">All Recon Tools</h2>
      <Link href="/tools/sandbox" className="m-0 mb-3 w-full p-0 text-center">
        <AppButton variant="outline" fullWidth className="m-0 p-0">
          Scaffold invariants Sandbox
        </AppButton>
      </Link>
      <h3 className="mb-4 pl-4 text-xl">Log Parsers</h3>
      <Link href="/tools/medusa" className="m-0 mb-3 w-full p-0 text-center">
        <AppButton variant="outline" fullWidth className="m-0 p-0">
          Medusa Log to Foundry
        </AppButton>
      </Link>
      <Link href="/tools/echidna" className="m-0 mb-3 w-full p-0 text-center">
        <AppButton variant="outline" fullWidth className="m-0 p-0">
          Echidna Log to Foundry
        </AppButton>
      </Link>
      <Link href="/tools/halmos" className="m-0 mb-3 w-full p-0 text-center">
        <AppButton variant="outline" fullWidth className="m-0 p-0">
          Halmos Log to Foundry
        </AppButton>
      </Link>
      <h3 className="mb-4 pl-4 text-xl">Tools</h3>
      <Link
        href="/tools/bytecode-compare"
        className="m-0 mb-3 w-full p-0 text-center"
      >
        <AppButton variant="outline" fullWidth className="m-0 p-0">
          Bytecode compare
        </AppButton>
      </Link>
      <Link
        href="/tools/bytecode-to-interface"
        className="m-0 mb-3 w-full p-0 text-center"
      >
        <AppButton variant="outline" fullWidth className="m-0 p-0">
          Bytecode to interface
        </AppButton>
      </Link>
    </>
  );
}

export default function AllToolsPage() {
  return (
    <div className="relative z-10 flex min-h-screen w-full flex-col items-center justify-center bg-black text-white">
      <ToolsList />
    </div>
  );
}
