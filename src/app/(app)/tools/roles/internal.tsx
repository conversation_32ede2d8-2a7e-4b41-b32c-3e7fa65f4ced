"use client";
import { useCallback, useState } from "react";
import Link from "next/link";
import { FiArrowLeft } from "react-icons/fi";

import { AppCheckbox } from "@/app/components/app-checkbox";
import { AppInput } from "@/app/components/app-input";
import { AppButton } from "@/app/components/app-button";
import { Body2, H1, H3 } from "@/app/components/app-typography";
import { GradientWrapper } from "@/app/components/gradient-wrapper";
import { AppHeader } from "@/app/(app)/components/app-header";
import {
  doKeccak256,
  getAllRolesEvents,
  getRoleMembersFromEnumerable,
  inferRolesFromSource,
} from "oz-roles-scraper";

export default function RolesPageInternal() {
  const [rpcUrl, setRpcUrl] = useState("https://eth.llamarpc.com");
  const [contractAddress, setContractAddress] = useState(
    "******************************************"
  );
  const [isEnumerable, setIsEnumerable] = useState(false);
  const [role, setRole] = useState("");
  const [roleHash, setRoleHash] = useState("");

  const [foundRoles, setFoundRoles] = useState<string[]>([]);
  const [roleMembers, setRoleMembers] = useState<string[]>([]);
  const [roleId, setRoleId] = useState<string>("");

  const [startBlock, setStartBlock] = useState<number>(18290587);
  const [isLoadingBatchEvents, setIsLoadingBatchEvents] =
    useState<boolean>(false);
  const [allRoleEvents, setAllRoleEvents] = useState<any>([]);

  const inferRoles = useCallback(async () => {
    if (!rpcUrl || !contractAddress) return [];
    const roles = await inferRolesFromSource(rpcUrl, contractAddress);

    setFoundRoles(roles.map((role) => role.name));
  }, [rpcUrl, contractAddress]);

  const fetchRoleMembers = useCallback(async () => {
    if (!rpcUrl || !contractAddress || !roleHash) return [];
    const result = await getRoleMembersFromEnumerable(
      rpcUrl,
      contractAddress,
      roleHash
    );
    setRoleMembers(result.members.map((member) => member));
  }, [rpcUrl, contractAddress, roleHash]);

  const generateRoleId = useCallback(async () => {
    if (!role) return;
    const result = await doKeccak256(role);
    setRoleId(result);
  }, [role]);

  const fetchRoleEvents = useCallback(async () => {
    if (!rpcUrl || !contractAddress) return [];
    setIsLoadingBatchEvents(true);
    const result = await getAllRolesEvents(
      rpcUrl,
      contractAddress,
      role,
      startBlock
    );
    setAllRoleEvents(result);
    setIsLoadingBatchEvents(false);
  }, [role, rpcUrl, contractAddress, startBlock]);

  return (
    <div className="main-container w-full overflow-x-hidden">
      <div className="relative z-10 min-h-screen">
        <AppHeader skipUser />

        <GradientWrapper className="flex min-h-[calc(100vh-80px)] items-center justify-center py-8">
          <div className="w-full max-w-4xl rounded-[20px] bg-back-neutral-tertiary px-[48px] py-[40px]">
            <div className="mb-8 text-left">
              <Link
                href="/"
                className="mb-6 inline-flex items-center text-fore-neutral-primary transition-colors duration-200 hover:text-fore-neutral-secondary"
              >
                <FiArrowLeft className="size-10" />
              </Link>

              <H1 className="mb-6 text-accent-primary">
                OpenZeppelin Roles Scraper
              </H1>

              <Body2 className="mb-2">
                This tool helps you analyze and extract role-based access
                control information from smart contracts
              </Body2>
              <Body2 className="mb-6">
                Infer roles from contract ABI, generate role IDs, fetch role
                members for enumerable roles
              </Body2>
            </div>
            <div className="mb-8">
              <AppInput
                className="mb-4"
                label="RPC URL"
                value={rpcUrl}
                onChange={(e) => setRpcUrl(e.target.value)}
                type="text"
              />

              <AppInput
                className="mb-6"
                label="Contract Address"
                value={contractAddress}
                onChange={(e) => setContractAddress(e.target.value)}
                type="text"
              />

              <AppCheckbox
                label="Is Enumerable"
                checked={isEnumerable}
                onChange={(e) => setIsEnumerable(e.target.checked)}
              />
            </div>

            {isEnumerable && (
              <div className="mb-8">
                <H3 className="mb-4 text-fore-neutral-primary">
                  Get Role Members (Enumerable)
                </H3>
                <AppInput
                  className="mb-4"
                  label="Role Hash"
                  value={roleHash}
                  onChange={(e) => setRoleHash(e.target.value)}
                />
                <AppButton onClick={fetchRoleMembers} className="mb-6">
                  Get Role Members
                </AppButton>

                {roleMembers.length > 0 && (
                  <div className="rounded-md bg-back-neutral-tertiary p-4">
                    <H3 className="mb-2 text-fore-neutral-primary">
                      Role Members:
                    </H3>
                    {roleMembers.map((member) => (
                      <Body2
                        key={member}
                        className="text-fore-neutral-secondary font-mono mb-1"
                      >
                        {member}
                      </Body2>
                    ))}
                  </div>
                )}
              </div>
            )}

            <div className="mb-8">
              <H3 className="mb-4 text-fore-neutral-primary">
                Scrape Roles from ABI
              </H3>
              <AppButton onClick={inferRoles} className="mb-6">
                Infer Roles
              </AppButton>

              {foundRoles.length > 0 && (
                <div className="rounded-md bg-back-neutral-tertiary p-4">
                  <H3 className="mb-2 text-fore-neutral-primary">
                    Found Roles:
                  </H3>
                  {foundRoles.map((role) => (
                    <Body2
                      key={role}
                      className="text-fore-neutral-secondary mb-1"
                    >
                      {role}
                    </Body2>
                  ))}
                </div>
              )}
            </div>

            <div className="mb-8">
              <H3 className="mb-4 text-fore-neutral-primary">
                Generate Role ID from Role Name
              </H3>
              <AppInput
                className="mb-4"
                label="Role Name"
                value={role}
                onChange={(e) => setRole(e.target.value)}
              />
              <AppButton onClick={generateRoleId} className="mb-6">
                Generate Role ID
              </AppButton>

              {roleId && (
                <div className="rounded-md bg-back-neutral-tertiary p-4">
                  <H3 className="mb-2 text-fore-neutral-primary">Role ID:</H3>
                  <Body2 className="text-fore-neutral-secondary font-mono break-all">
                    {roleId}
                  </Body2>
                </div>
              )}
            </div>
          </div>
        </GradientWrapper>
      </div>
    </div>
  );
}
