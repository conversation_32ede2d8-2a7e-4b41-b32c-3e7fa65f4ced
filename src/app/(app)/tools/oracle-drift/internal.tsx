"use client";
import Link from "next/link";
import { useMemo, useState } from "react";
import { FiArrowLeft } from "react-icons/fi";

import { AppHeader } from "@/app/(app)/components/app-header";
import { AppButton } from "@/app/components/app-button";
import { AppInput } from "@/app/components/app-input";
import { Body2, H1, H3 } from "@/app/components/app-typography";
import { GradientWrapper } from "@/app/components/gradient-wrapper";
import {
  defaultFormula,
  evaluateFormula,
  formulaExamples,
} from "./formula-evaluator";
import { doubleDrift } from "./lib";

export default function OracleDriftPageInternal() {
  const [oracleOnePrice, setOracleOnePrice] = useState<any>("358189942348");
  const [oracleOneDeviationThresholdBPS, setOracleOneDeviationThresholdBPS] =
    useState<any>("25");
  const [oracleTwoPrice, setOracleTwoPrice] = useState<any>("99989574");
  const [oracleTwoDeviationThresholdBPS, setOracleTwoDeviationThresholdBPS] =
    useState<any>("50");
  const [customFormula, setCustomFormula] = useState<string>(defaultFormula);
  const [formulaError, setFormulaError] = useState<string>("");

  const validateAndCall = (
    min: number,
    max: number,
    value: number,
    fn: (input: any) => any
  ) => {
    if (value < min || value > max) {
      return false;
    }
    return fn(value);
  };

  const formulaFn = useMemo(() => {
    try {
      setFormulaError("");
      // Test the formula with sample values to validate it
      evaluateFormula(customFormula, { coll: 1, debt: 1 });
      return (coll: number, debt: number) =>
        evaluateFormula(customFormula, { coll, debt });
    } catch (error) {
      setFormulaError(error.message);
      return null;
    }
  }, [customFormula]);

  const result = useMemo(() => {
    if (!formulaFn) {
      return { spot: 0, max: 0, min: 0 };
    }
    try {
      return doubleDrift(
        Number(oracleOnePrice),
        Number(oracleOneDeviationThresholdBPS),
        Number(oracleTwoPrice),
        Number(oracleTwoDeviationThresholdBPS),
        formulaFn
      );
    } catch (error) {
      setFormulaError(`Error calculating result: ${error.message}`);
      return { spot: 0, max: 0, min: 0 };
    }
  }, [
    oracleOnePrice,
    oracleOneDeviationThresholdBPS,
    oracleTwoPrice,
    oracleTwoDeviationThresholdBPS,
    formulaFn,
  ]);

  return (
    <div className="main-container w-full overflow-x-hidden">
      <div className="relative z-10 min-h-screen">
        <AppHeader skipUser />

        <GradientWrapper className="flex min-h-[calc(100vh-80px)] items-center justify-center py-8">
          <div className="w-full max-w-6xl rounded-[20px] bg-back-neutral-tertiary px-[48px] py-[40px]">
            <div className="mb-8 text-left">
              <Link
                href="/"
                className="mb-6 inline-flex items-center text-fore-neutral-primary transition-colors duration-200 hover:text-fore-neutral-secondary"
              >
                <FiArrowLeft className="size-10" />
              </Link>

              <H1 className="mb-6 text-accent-primary">Oracle Drift</H1>

              <Body2 className="mb-2">
                Given 2 Oracle prices and their Deviation Threshold
              </Body2>
              <Body2 className="mb-2">
                The tool will calculate the max and min possible values for the
                oracle, and the max delta between the two.
              </Body2>
              <Body2 className="mb-6">
                NOTE: You can alter the formula by typing in the textarea or
                clicking the buttons for presets
              </Body2>
            </div>

            <div className="mb-8">
              <H3 className="mb-4 text-fore-neutral-primary">
                Step 1: Configure Formula
              </H3>
              <div className="mb-6">
                <AppInput
                  className="mb-4"
                  label="Custom Formula (use 'coll' for Oracle One, 'debt' for Oracle Two)"
                  value={customFormula}
                  onChange={(e) => setCustomFormula(e.target.value)}
                  type="text"
                  placeholder="e.g., coll / debt"
                />
                {formulaError && (
                  <div className="text-status-error mt-2 p-3 bg-status-error/10 rounded">
                    {formulaError}
                  </div>
                )}
              </div>

              <div className="flex flex-wrap gap-2">
                <Body2 className="text-fore-neutral-secondary mb-2">
                  Examples:
                </Body2>
                <div className="flex flex-wrap gap-2">
                  {formulaExamples.map((example) => (
                    <AppButton
                      key={example.name}
                      onClick={() => setCustomFormula(example.formula)}
                      variant="secondary"
                      size="xs"
                    >
                      {example.name}: {example.formula}
                    </AppButton>
                  ))}
                </div>
              </div>
            </div>

            <div className="mb-8">
              <H3 className="mb-4 text-fore-neutral-primary">
                Step 2: Configure Oracle Parameters
              </H3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <AppInput
                  className="mb-4"
                  label="Collateral Price"
                  value={oracleOnePrice}
                  type="text"
                  onChange={(e) =>
                    validateAndCall(
                      0,
                      Infinity,
                      Number(e.target.value),
                      setOracleOnePrice
                    )
                  }
                />
                <AppInput
                  className="mb-4"
                  label="Collateral Deviation Threshold (BPS)"
                  value={oracleOneDeviationThresholdBPS}
                  onChange={(e) =>
                    validateAndCall(
                      0,
                      10000,
                      Number(e.target.value),
                      setOracleOneDeviationThresholdBPS
                    )
                  }
                  type="text"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <AppInput
                  className="mb-4"
                  label="Debt Price"
                  value={oracleTwoPrice}
                  onChange={(e) =>
                    validateAndCall(
                      0,
                      Infinity,
                      Number(e.target.value),
                      setOracleTwoPrice
                    )
                  }
                  type="text"
                />
                <AppInput
                  className="mb-4"
                  label="Debt Deviation Threshold (BPS)"
                  value={oracleTwoDeviationThresholdBPS}
                  onChange={(e) =>
                    validateAndCall(
                      0,
                      10000,
                      Number(e.target.value),
                      setOracleTwoDeviationThresholdBPS
                    )
                  }
                  type="text"
                />
              </div>
            </div>

            <div className="mb-8">
              <H3 className="mb-4 text-fore-neutral-primary">
                Step 3: Results
              </H3>
              {formulaError ? (
                <div className="rounded-md bg-status-error/10 border border-status-error p-4">
                  <Body2 className="text-status-error font-semibold">
                    Formula Error
                  </Body2>
                  <Body2 className="text-status-error mt-1">
                    {formulaError}
                  </Body2>
                  <Body2 className="text-status-error mt-2">
                    Please correct the formula to see results.
                  </Body2>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="p-4 rounded-lg bg-back-neutral-secondary border border-stroke-neutral-decorative">
                    <Body2 className="text-fore-neutral-secondary mb-1">
                      Face Value
                    </Body2>
                    <Body2 className="text-fore-neutral-primary font-semibold text-lg">
                      {result.spot}
                    </Body2>
                  </div>
                  <div className="p-4 rounded-lg bg-back-neutral-secondary border border-stroke-neutral-decorative">
                    <Body2 className="text-fore-neutral-secondary mb-1">
                      Max Possible Value
                    </Body2>
                    <Body2 className="text-fore-neutral-primary font-semibold text-lg">
                      {result.max}
                    </Body2>
                  </div>
                  <div className="p-4 rounded-lg bg-back-neutral-secondary border border-stroke-neutral-decorative">
                    <Body2 className="text-fore-neutral-secondary mb-1">
                      Min Possible Value
                    </Body2>
                    <Body2 className="text-fore-neutral-primary font-semibold text-lg">
                      {result.min}
                    </Body2>
                  </div>
                  <div className="p-4 rounded-lg bg-back-neutral-secondary border border-stroke-neutral-decorative">
                    <Body2 className="text-fore-neutral-secondary mb-1">
                      Max Delta
                    </Body2>
                    <Body2 className="text-fore-neutral-primary font-semibold text-lg">
                      {((result.max / result.min) * 100).toFixed(2)}%
                    </Body2>
                  </div>
                  <div className="p-4 rounded-lg bg-back-neutral-secondary border border-stroke-neutral-decorative">
                    <Body2 className="text-fore-neutral-secondary mb-1">
                      Max Up
                    </Body2>
                    <Body2 className="text-fore-neutral-primary font-semibold text-lg">
                      {((result.max / result.spot) * 100).toFixed(2)}%
                    </Body2>
                  </div>
                  <div className="p-4 rounded-lg bg-back-neutral-secondary border border-stroke-neutral-decorative">
                    <Body2 className="text-fore-neutral-secondary mb-1">
                      Max Down
                    </Body2>
                    <Body2 className="text-fore-neutral-primary font-semibold text-lg">
                      {((result.min / result.spot) * 100).toFixed(2)}%
                    </Body2>
                  </div>
                </div>
              )}
            </div>
          </div>
        </GradientWrapper>
      </div>
    </div>
  );
}
