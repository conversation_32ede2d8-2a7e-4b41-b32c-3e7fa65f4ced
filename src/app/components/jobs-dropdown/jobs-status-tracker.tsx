/* eslint-disable tailwindcss/no-custom-classname */
// TODO Figure out what people want

import noop from "lodash/noop";
import { FaCheckCircle, FaPlayCircle } from "react-icons/fa";
import { IoListCircleSharp } from "react-icons/io5";
import { MdError } from "react-icons/md";

import type { AbiJob } from "@/app/services/abi-jobs-service";
import { groupJobs } from "@/app/services/abi-jobs-service";
import type { Job } from "@/app/services/jobs.hooks";

const TWENTY_FOUR_HOURS_MS = 24 * 3600 * 1000;
const FILTER_END_DATA = TWENTY_FOUR_HOURS_MS;

function filterByFinished(jobsData: Array<Job | AbiJob>) {
  if (jobsData?.length > 0) {
    return jobsData.filter((entry) => {
      const haveEnded =
        entry.status === "STOPPED" ||
        entry.status === "SUCCESS" ||
        entry.status === "ERROR";
      const isRecentEnough =
        new Date().getTime() - new Date(entry.updatedAt).getTime() <
        FILTER_END_DATA;

      return haveEnded && isRecentEnough;
    });
  }

  return [];
}

const JOB_GROUP_ICON_MAP = {
  QUEUED: <IoListCircleSharp className="text-gold size-[14px]" />,
  RUNNING: <FaPlayCircle className="text-blue size-[14px]" />,
  FINISHED: <FaCheckCircle className="size-[14px] text-status-success" />,
  ERROR: <MdError className="size-[14px] text-status-error" />,
};

type JobStatusTrackerProps = {
  jobs: Job[];
  abiJobs: AbiJob[];
};

type JobGroup = {
  label: string;
  type: string;
  jobs: Array<Job | AbiJob>;
};

const ONE_HOUR_MS = 1000 * 60 * 60; // 1 hour
const ONE_DAY_MS = ONE_HOUR_MS * 24;

function filterByUpdatedAt(jobs: (Job | AbiJob)[]): (Job | AbiJob)[] {
  return jobs?.filter(
    (event) =>
      new Date(event.updatedAt).getTime() > new Date().getTime() - ONE_DAY_MS
  );
}

export function JobStatusTracker({ jobs, abiJobs }: JobStatusTrackerProps) {
  const jobsMap = groupJobs(jobs);
  const recentlyFinishedJobs = filterByFinished(jobs);

  const abiJobsMap = groupJobs(abiJobs);
  const recentlyFinishedAbiJobs = filterByFinished(abiJobs);

  const jobGroups = [
    {
      label: "Queued Jobs",
      type: "QUEUED",
      jobs: jobsMap?.QUEUED,
    },
    {
      label: "Running Jobs",
      type: "RUNNING",
      jobs: filterByUpdatedAt(jobsMap?.RUNNING), // Hide jobs that are running that don't give status updates, they are prob broken
    },
    {
      label: "Recently Finished Jobs",
      type: "FINISHED",
      jobs: recentlyFinishedJobs,
    },
    {
      label: "Errored Jobs",
      type: "ERROR",
      jobs: filterByUpdatedAt(jobsMap?.ERROR),
    },
  ];

  const abiJobGroups = [
    {
      label: "ABI Being Built",
      type: "RUNNING",
      jobs: abiJobsMap?.RUNNING,
    },
    {
      label: "Errored Abi Build",
      type: "ERROR",
      jobs: filterByUpdatedAt(abiJobsMap?.ERROR),
    },
    {
      label: "Recently Finished Jobs",
      type: "FINISHED",
      jobs: recentlyFinishedAbiJobs,
    },
  ];

  const renderJobRow = (label: string, value: string) => (
    <div className="mb-[5px]">
      <span className="block text-[13px] leading-[15px] text-fore-neutral-secondary">
        {label}
      </span>
      <span className="block text-[14px] leading-[18px] text-fore-neutral-primary">
        {value}
      </span>
    </div>
  );

  const renderJobGroup = (jobGroups: JobGroup[]) => {
    return jobGroups.map(({ jobs, label, type }) => {
      const Icon = JOB_GROUP_ICON_MAP[type];

      if (!jobs?.length) return null;

      return (
        <>
          <div className="h-px bg-divider" />
          <div key={label} className="px-[15px] py-[14px]">
            <h4 className="mb-[8px] mt-[12px] flex items-center text-[16px] leading-[19px] text-fore-neutral-primary">
              {Icon}
              <span className="ml-[12px] block">{label}</span>
              <button
                className="leafing-[16px] ml-auto cursor-pointer text-[14px] text-accent-primary underline"
                onClick={noop}
              >
                Clear
              </button>
            </h4>
            {jobs?.map(({ id, orgName, repoName, updatedAt }) => (
              <div key={id} className="mb-[15px]">
                <div className="mb-[5px] h-px bg-divider" />
                {renderJobRow("Organization", orgName)}
                {renderJobRow("Repository", repoName)}
                {renderJobRow("Last Updated", updatedAt)}
              </div>
            ))}
          </div>
        </>
      );
    });
  };

  return (
    <div className="max-h-[400px] w-full overflow-y-auto bg-fore-on-accent-primary">
      {renderJobGroup(jobGroups)}
      {renderJobGroup(abiJobGroups)}
    </div>
  );
}
