"use client";
import axios from "axios";
import { useState } from "react";

import { cn } from "../helpers/cn";
import { AppButton } from "./app-button";
import { AppListCheckItem } from "./app-list-check-item";
import { AppSpinner } from "./app-spinner";

export const InviteInProOrg = () => {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [data, setData] = useState(null);

  const createNewInvite = async (e) => {
    e.preventDefault();

    if (loading) {
      return;
    }
    setLoading(true);
    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/orgInvites`,
      });

      setMessage(foundData?.data?.message);
      setData(foundData?.data?.data);
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
      setLoading(false);
    }
  };

  return (
    <div>
      <h1 className="mt-[40px] text-[23px] leading-[25px] text-fore-neutral-primary">
        Create a new invite
      </h1>

      <ul className="my-[10px]">
        {[
          " NOTE! Invite codes give access to your Repo and ABIS!! ",
          "Do not share them unless you want them to join the org!",
          "Each code is one time use!",
        ].map((text) => (
          <AppListCheckItem text={text} key={text} />
        ))}
      </ul>

      <AppButton
        onClick={createNewInvite}
        disabled={loading}
        className="my-[10px]"
      >
        {loading ? <AppSpinner /> : "Create new Code"}
      </AppButton>

      {message && (
        <div
          className={cn(
            "my-[20px] rounded-[5px] bg-yellow-500 p-3 text-fore-neutral-primary",
            {
              "bg-red-500": !data,
              "bg-green-500": data,
            }
          )}
        >
          {message}
        </div>
      )}
    </div>
  );
};
