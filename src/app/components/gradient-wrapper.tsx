"use client";

import type { ReactNode } from "react";
import { cn } from "../helpers/cn";
import Aurora from "./aurora-bg";

interface GradientWrapperProps {
  children?: ReactNode;
  className?: string;
}

export function GradientWrapper({ children, className }: GradientWrapperProps) {
  return (
    <div className={cn("size-full", className)}>
      <Aurora
        colorStops={["#5C25D2", "#9d6bff", "#20006a"]}
        amplitude={1.5}
        blend={1.5}
      />
      {children}
    </div>
  );
}
